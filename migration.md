# Database Migrations Guide

This document provides comprehensive information about managing database migrations in the List Maker Server project using Drizzle ORM and Drizzle Kit.

## Overview

The project uses Drizzle ORM with MySQL for database operations and Drizzle Kit for migration management. All database schema changes should be managed through migrations to ensure consistency across different environments.

## Configuration

### Drizzle Configuration
The migration system is configured in `drizzle.config.js`:
- **Schema**: `./src/models/schema.js` - Central schema file
- **Output**: `./migrations` - Migration files directory
- **Dialect**: `mysql` - Database type
- **Migration Table**: `drizzle_migrations` - Tracks applied migrations

### Environment Variables
Ensure these variables are set in your `.env` file:
```
DATABASE_HOST=localhost
DATABASE_USER=your_username
DATABASE_PASSWORD=your_password
DATABASE_NAME=your_database_name
DATABASE_PORT=3306
```

## Migration Commands

### Core Migration Commands

#### Generate Migration
Creates a new migration file based on schema changes:
```bash
npm run db:generate
```
This command:
- Compares current schema with the database
- Generates SQL migration files in the `migrations/` directory
- Creates migration metadata in `migrations/meta/`

#### Apply Migrations
Applies pending migrations to the database:
```bash
npm run db:up
```
This command:
- Runs all pending migrations in order
- Updates the migration tracking table
- Cannot be undone automatically

**Note**: For this version of drizzle-kit, we use `db:up` instead of `db:migrate`

#### Alternative Migration Runner
We also provide a custom migration script for more control:
```bash
npm run db:migrate
```
This uses our custom `scripts/migrate.js` which provides better error handling and logging.

#### Push Schema (Development Only)
Directly pushes schema changes without generating migration files:
```bash
npm run db:push
```
⚠️ **Warning**: Only use in development. This bypasses migration tracking.

### Additional Commands

#### Database Studio
Opens Drizzle Studio for database inspection:
```bash
npm run db:studio
```
Provides a web interface to view and edit database data.

#### Check Migrations
Validates migration files and checks for issues:
```bash
npm run db:check
```

#### Drop Database
Drops all tables (destructive operation):
```bash
npm run db:drop
```
⚠️ **Danger**: This will delete all data. Use with extreme caution.

#### Migration Status
Check which migrations have been applied:
```bash
npm run db:up
```

#### Introspect Database
Generate schema from existing database:
```bash
npm run db:introspect
```

## Migration Workflow

### 1. Making Schema Changes
1. Modify your schema files in `src/models/`
2. Update the central schema export in `src/models/schema.js` if needed
3. Generate a migration: `npm run db:generate`
4. Review the generated migration files
5. Apply the migration: `npm run db:up`

### 2. Schema File Structure
```
src/models/
├── schema.js          # Central schema export
├── list.js           # List table schema
├── task.js           # Task table schema
├── tag.js            # Tag table schema
└── task_tag.js       # TaskTag junction table schema
```

### 3. Migration File Structure
```
migrations/
├── 0000_initial_schema.sql
├── 0001_add_user_table.sql
├── 0002_add_indexes.sql
└── meta/
    ├── _journal.json
    └── 0000_snapshot.json
```

## Best Practices

### Schema Changes
1. **Always generate migrations** for schema changes
2. **Review migration files** before applying them
3. **Test migrations** in development first
4. **Backup production data** before applying migrations
5. **Never edit applied migration files**

### Migration Naming
Drizzle Kit automatically names migrations with timestamps and descriptions:
- Format: `XXXX_description.sql`
- Example: `0001_add_user_authentication.sql`

### Development vs Production

#### Development Environment
- Use `npm run db:push` for rapid prototyping
- Generate proper migrations before committing
- Reset database when needed: `npm run db:drop` then `npm run db:migrate`

#### Production Environment
- **Only use** `npm run db:up`
- **Never use** `npm run db:push` or `npm run db:drop`
- Always backup before migrations
- Test migrations in staging first

## Common Scenarios

### Adding a New Table
1. Create the table schema in `src/models/new_table.js`
2. Export it in `src/models/schema.js`
3. Generate migration: `npm run db:generate`
4. Apply migration: `npm run db:up`

### Modifying Existing Table
1. Update the table schema in the respective model file
2. Generate migration: `npm run db:generate`
3. Review the generated SQL carefully
4. Apply migration: `npm run db:up`

### Adding Indexes
1. Add index definitions to your table schema
2. Generate migration: `npm run db:generate`
3. Apply migration: `npm run db:up`

### Data Migrations
For complex data transformations:
1. Generate schema migration first
2. Create custom SQL scripts for data migration
3. Apply in correct order

## Troubleshooting

### Common Issues

#### Migration Conflicts
- Ensure all team members apply migrations before making new changes
- Resolve conflicts by coordinating schema changes

#### Failed Migrations
- Check database connection
- Verify environment variables
- Review migration SQL for syntax errors
- Check database permissions

#### Schema Drift
- Use `npm run db:check` to detect inconsistencies
- Regenerate migrations if needed
- Consider using `npm run db:push` in development to reset

### Recovery Procedures

#### Rollback Strategy
Drizzle doesn't provide automatic rollbacks. For rollbacks:
1. Create a new migration that reverses changes
2. Or restore from database backup
3. Or manually write reverse SQL

#### Reset Development Database
```bash
npm run db:drop
npm run db:up
```

## Team Collaboration

### Git Workflow
1. **Commit migration files** with schema changes
2. **Pull latest migrations** before making changes
3. **Apply migrations** after pulling: `npm run db:up`
4. **Coordinate** complex schema changes with team

### Code Review
- Review both schema changes and generated migrations
- Ensure migrations are safe and reversible
- Check for performance implications

## Monitoring and Maintenance

### Migration History
- Track applied migrations in `drizzle_migrations` table
- Keep migration files in version control
- Document major schema changes

### Performance Considerations
- Add indexes for frequently queried columns
- Consider migration impact on large tables
- Plan maintenance windows for major changes

## Security Notes

- Never commit database credentials
- Use environment variables for configuration
- Restrict database permissions appropriately
- Backup before production migrations

## Support and Resources

- [Drizzle ORM Documentation](https://orm.drizzle.team/)
- [Drizzle Kit Documentation](https://orm.drizzle.team/kit-docs/overview)
- [MySQL Documentation](https://dev.mysql.com/doc/)

For project-specific questions, refer to the development team or project documentation.
