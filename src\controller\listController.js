import { v4 as uuid } from 'uuid';
import { List } from '../models/list.js';
import { db } from '../config/db.js';
import { eq, and, inArray, isNull } from 'drizzle-orm';
import { Task } from '../models/task.js';
import { TaskTag } from '../models/task_tag.js';
import { Tag } from '../models/tag.js';

const createList = async (req, res) => {
  const { user_id, title, show_archived } = req.body;

  console.log({ user_id, title, show_archived });

  if (!user_id || !title) {
    return res.status(400).json({ error: 'User ID and Title are required.' });
  }

  try {
    const listId = uuid();
    const listData = {
      id: listId,
      user_id,
      title,
      show_archived: show_archived || false,
    };

    await db.insert(List).values(listData);

    const [newList] = await db.select().from(List).where(eq(List.id, listId));

    if (!newList) {
      return res.status(400).json({ error: 'Failed to insert list.' });
    }

    return res.status(201).json({
      success: true,
      message: 'List created successfully',
      data: newList,
    });
  } catch (error) {
    console.error('Error creating list:', error);
    return res.status(500).json({ error: 'Failed to create list' });
  }
};

const getLists = async (req, res) => {
  try {
    const { user_id, archived = "false" } = req.query;
    const show_archived = archived === "true";

    if (!user_id) {
      return res.status(400).json({ success: false, error: 'User ID is required.' });
    }

    const lists = await db.select().from(List).where(
      and(
        eq(List.user_id, user_id), 
        eq(List.show_archived, show_archived),
        isNull(List.deleted_at)
      )
    );

    if (lists.length === 0) {
      return res.status(200).json({
        success: true,
        count: 0,
        data: [],
      });
    }

    const listIds = lists.map(list => list.id);

    const [tasks, listTags] = await Promise.all([
      db.select().from(Task).where(
        and(
          inArray(Task.list_id, listIds),
          isNull(Task.deleted_at)
        )
      ),
      db.select().from(Tag).where(
        and(
          inArray(Tag.list_id, listIds),
          isNull(Tag.deleted_at)
        )
      )
    ]);

    const taskIds = tasks.map(task => task.id);
    let taskTags = [];
    
    if (taskIds.length > 0) {
      taskTags = await db
        .select({
          task_id: TaskTag.task_id,
          tag_id: Tag.id,
          tag_name: Tag.name,
          tag_color: Tag.color,
          tag_list_id: Tag.list_id
        })
        .from(TaskTag)
        .innerJoin(Tag, eq(TaskTag.tag_id, Tag.id))
        .where(
          and(
            inArray(TaskTag.task_id, taskIds),
            isNull(Tag.deleted_at)
          )
        );
    }

    const tagsByListId = listTags.reduce((acc, tag) => {
      if (!acc[tag.list_id]) {
        acc[tag.list_id] = [];
      }
      acc[tag.list_id].push(tag);
      return acc;
    }, {});

    const tagsByTaskId = taskTags.reduce((acc, tt) => {
      if (!acc[tt.task_id]) {
        acc[tt.task_id] = [];
      }
      acc[tt.task_id].push({
        id: tt.tag_id,
        name: tt.tag_name,
        color: tt.tag_color,
        list_id: tt.tag_list_id
      });
      return acc;
    }, {});

    const tasksWithTags = tasks.map(task => ({
      ...task,
      tags: tagsByTaskId[task.id] || []
    }));

    const tasksByListId = tasksWithTags.reduce((acc, task) => {
      if (!acc[task.list_id]) {
        acc[task.list_id] = [];
      }
      acc[task.list_id].push(task);
      return acc;
    }, {});

    const listsWithData = lists.map(list => ({
      ...list,
      tasks: tasksByListId[list.id] || [],
      tags: tagsByListId[list.id] || []
    }));

    return res.status(200).json({
      success: true,
      count: listsWithData.length,
      data: listsWithData,
    });
  } catch (error) {
    console.error('Error fetching lists:', error);
    return res.status(500).json({ 
      success: false, 
      error: 'Failed to fetch lists',
      message: error.message 
    });
  }
};


const getListById = async (req, res) => {
  const { id } = req.params;

  if (!id) {
    return res.status(400).json({ error: 'List ID is required.' });
  }

  try {
    const [list] = await db.select().from(List).where(eq(List.id, id));

    if (!list) {
      return res.status(404).json({ error: 'List not found' });
    }

    return res.status(200).json({
      success: true,
      data: list,
    });
  } catch (error) {
    console.error('Error fetching list:', error);
    return res.status(500).json({ error: 'Failed to fetch list' });
  }
};

const updateList = async (req, res) => {
  const { id } = req.params;
  const { title, show_archived } = req.body;

  if (!id) {
    return res.status(400).json({ error: 'List ID is required.' });
  }

  try {
    const [existingList] = await db.select().from(List).where(eq(List.id, id));

    if (!existingList) {
      return res.status(404).json({ error: 'List not found' });
    }

    const updateData = {};
    if (title !== undefined) updateData.title = title;
    if (show_archived !== undefined) updateData.show_archived = show_archived;
    updateData.updated_at = new Date();

    await db.update(List)
      .set(updateData)
      .where(eq(List.id, id));

    const [updatedList] = await db.select().from(List).where(eq(List.id, id));

    return res.status(200).json({
      success: true,
      message: 'List updated successfully',
      data: updatedList,
    });
  } catch (error) {
    console.error('Error updating list:', error);
    return res.status(500).json({ error: 'Failed to update list' });
  }
};

const deleteList = async (req, res) => {
  const { id } = req.params;
 
  if (!id) {
    return res.status(400).json({ error: 'List ID is required.' });
  }
 
  try {
    const [existingList] = await db.select().from(List).where(eq(List.id, id));
 
    if (!existingList) {
      return res.status(404).json({ error: 'List not found' });
    }
    const currentTimestamp = new Date();
 
    await db.transaction(async (tx) => {
      const tasksToUpdate = await tx
        .select({ id: Task.id })
        .from(Task)
        .where(eq(Task.list_id, id));
 
      const tagsToUpdate = await tx
        .select({ id: Tag.id })
        .from(Tag)
        .where(eq(Tag.list_id, id)); 
      
      const taskIds = tasksToUpdate.map(task => task.id);
      const tagIds = tagsToUpdate.map(tag => tag.id);
 
      if (taskIds.length > 0) {
        await tx.update(TaskTag).set({ deleted_at: currentTimestamp }).where(inArray(TaskTag.task_id, taskIds));
        await tx
          .update(Task)
          .set({ deleted_at: currentTimestamp })
          .where(eq(Task.list_id, id));
      }
 
      if (tagIds.length > 0) {
        await tx.update(TaskTag).set({ deleted_at: currentTimestamp }).where(inArray(TaskTag.tag_id, tagIds));
        await tx
          .update(Tag)
          .set({ deleted_at: currentTimestamp })
          .where(eq(Tag.list_id, id));
      }
      
      await tx
        .update(List)
        .set({ deleted_at: currentTimestamp })
        .where(eq(List.id, id));
    });
 
    return res.status(200).json({
      success: true,
      message: 'List and all associated data deleted successfully'
    });
  } catch (error) {
    console.error('Error soft-deleting list:', error);
    return res.status(500).json({ error: 'Failed to delete list' });
  }
};

export default {
  createList,
  getLists,
  getListById,
  updateList,
  deleteList,
};