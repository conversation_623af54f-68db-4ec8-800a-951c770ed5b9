{"version": "5", "dialect": "mysql", "id": "ee56ff8f-2028-414c-84a7-c2fa7eb879a2", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"list": {"name": "list", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(now())"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(now())"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}, "show_archived": {"name": "show_archived", "type": "boolean", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"list_id": {"name": "list_id", "columns": ["id"]}}, "uniqueConstraints": {}}, "tag": {"name": "tag", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true, "autoincrement": false}, "list_id": {"name": "list_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "color": {"name": "color", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"list_id_idx": {"name": "list_id_idx", "columns": ["list_id"], "isUnique": false}}, "foreignKeys": {"tag_list_id_list_id_fk": {"name": "tag_list_id_list_id_fk", "tableFrom": "tag", "tableTo": "list", "columnsFrom": ["list_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"tag_id": {"name": "tag_id", "columns": ["id"]}}, "uniqueConstraints": {}}, "task": {"name": "task", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true, "autoincrement": false}, "list_id": {"name": "list_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true, "autoincrement": false}, "text": {"name": "text", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "completed": {"name": "completed", "type": "boolean", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "archived": {"name": "archived", "type": "boolean", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(now())"}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}, "archived_at": {"name": "archived_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}, "due_date": {"name": "due_date", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}, "note": {"name": "note", "type": "<PERSON><PERSON><PERSON>(1000)", "primaryKey": false, "notNull": false, "autoincrement": false}, "priority": {"name": "priority", "type": "enum('low','normal','high')", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'normal'"}, "duration": {"name": "duration", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "recurrence_type": {"name": "recurrence_type", "type": "enum('daily','weekly','monthly','yearly')", "primaryKey": false, "notNull": false, "autoincrement": false}, "recurrence_interval": {"name": "recurrence_interval", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "recurrence_days_of_week": {"name": "recurrence_days_of_week", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "autoincrement": false}, "recurrence_ends_after": {"name": "recurrence_ends_after", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "recurrence_ends_on": {"name": "recurrence_ends_on", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}, "parent_task_id": {"name": "parent_task_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": false, "autoincrement": false}, "estimated_length": {"name": "estimated_length", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "in_progress": {"name": "in_progress", "type": "enum('not-started','in-progress','completed')", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'not-started'"}, "pomodoro_sessions": {"name": "pomodoro_sessions", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"task_list_id_idx": {"name": "task_list_id_idx", "columns": ["list_id"], "isUnique": false}}, "foreignKeys": {"task_list_id_list_id_fk": {"name": "task_list_id_list_id_fk", "tableFrom": "task", "tableTo": "list", "columnsFrom": ["list_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "task_parent_task_id_task_id_fk": {"name": "task_parent_task_id_task_id_fk", "tableFrom": "task", "tableTo": "task", "columnsFrom": ["parent_task_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {"task_id": {"name": "task_id", "columns": ["id"]}}, "uniqueConstraints": {}}, "task_tag": {"name": "task_tag", "columns": {"task_id": {"name": "task_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true, "autoincrement": false}, "tag_id": {"name": "tag_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true, "autoincrement": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"task_tag_task_id_tag_id_pk": {"name": "task_tag_task_id_tag_id_pk", "columns": ["task_id", "tag_id"]}}, "uniqueConstraints": {}}}, "schemas": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}}