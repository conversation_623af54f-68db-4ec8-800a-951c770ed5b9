import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

export default {
  schema: './src/models/schema.js',
  out: './migrations',
  driver: 'mysql2',
  dbCredentials: {
    host: process.env.DATABASE_HOST,
    user: process.env.DATABASE_USER,
    password: process.env.DATABASE_PASSWORD,
    database: process.env.DATABASE_NAME,
    port: parseInt(process.env.DATABASE_PORT) || 3306,
  },
  verbose: true,
  strict: true,
};
