CREATE TABLE `list` (
	`id` varchar(36) NOT NULL,
	`user_id` varchar(255) NOT NULL,
	`title` varchar(255) NOT NULL,
	`created_at` timestamp DEFAULT (now()),
	`updated_at` timestamp DEFAULT (now()),
	`deleted_at` timestamp,
	`show_archived` boolean DEFAULT false,
	CONSTRAINT `list_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `tag` (
	`id` varchar(36) NOT NULL,
	`list_id` varchar(36) NOT NULL,
	`name` varchar(255) NOT NULL,
	`color` varchar(50) NOT NULL,
	`deleted_at` timestamp,
	CONSTRAINT `tag_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `task` (
	`id` varchar(36) NOT NULL,
	`list_id` varchar(36) NOT NULL,
	`text` varchar(255) NOT NULL,
	`completed` boolean DEFAULT false,
	`archived` boolean DEFAULT false,
	`created_at` timestamp DEFAULT (now()),
	`completed_at` timestamp,
	`archived_at` timestamp,
	`due_date` timestamp,
	`note` varchar(1000),
	`priority` enum('low','normal','high') DEFAULT 'normal',
	`duration` int,
	`recurrence_type` enum('daily','weekly','monthly','yearly'),
	`recurrence_interval` int,
	`recurrence_days_of_week` varchar(20),
	`recurrence_ends_after` int,
	`recurrence_ends_on` timestamp,
	`parent_task_id` varchar(36),
	`estimated_length` int,
	`in_progress` enum('not-started','in-progress','completed') DEFAULT 'not-started',
	`pomodoro_sessions` int,
	`deleted_at` timestamp,
	CONSTRAINT `task_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `task_tag` (
	`task_id` varchar(36) NOT NULL,
	`tag_id` varchar(36) NOT NULL,
	`deleted_at` timestamp,
	CONSTRAINT `task_tag_task_id_tag_id_pk` PRIMARY KEY(`task_id`,`tag_id`)
);
--> statement-breakpoint
CREATE INDEX `list_id_idx` ON `tag` (`list_id`);--> statement-breakpoint
CREATE INDEX `task_list_id_idx` ON `task` (`list_id`);--> statement-breakpoint
ALTER TABLE `tag` ADD CONSTRAINT `tag_list_id_list_id_fk` FOREIGN KEY (`list_id`) REFERENCES `list`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `task` ADD CONSTRAINT `task_list_id_list_id_fk` FOREIGN KEY (`list_id`) REFERENCES `list`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `task` ADD CONSTRAINT `task_parent_task_id_task_id_fk` FOREIGN KEY (`parent_task_id`) REFERENCES `task`(`id`) ON DELETE set null ON UPDATE no action;