#!/usr/bin/env node

/**
 * Migration runner script for Drizzle ORM
 * This script provides a simple interface to run database migrations
 */

import { drizzle } from 'drizzle-orm/mysql2';
import { migrate } from 'drizzle-orm/mysql2/migrator';
import mysql from 'mysql2/promise';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function runMigrations() {
  console.log('🚀 Starting database migrations...');
  
  try {
    // Create database connection
    const connection = await mysql.createConnection({
      host: process.env.DATABASE_HOST,
      user: process.env.DATABASE_USER,
      password: process.env.DATABASE_PASSWORD,
      database: process.env.DATABASE_NAME,
      port: parseInt(process.env.DATABASE_PORT) || 3306,
    });

    // Create drizzle instance
    const db = drizzle(connection);

    // Run migrations
    const migrationsFolder = join(__dirname, '..', 'migrations');
    await migrate(db, { migrationsFolder });

    console.log('✅ Migrations completed successfully!');
    
    // Close connection
    await connection.end();
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Run migrations if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runMigrations();
}

export { runMigrations };
