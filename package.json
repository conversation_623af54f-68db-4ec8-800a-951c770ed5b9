{"name": "express-backend", "version": "1.0.0", "type": "module", "scripts": {"dev": "node --watch src/index.js", "start": "node src/index.js", "lint": "eslint src/**/*.js", "db:generate": "drizzle-kit generate:mysql", "db:push": "drizzle-kit push:mysql", "db:studio": "drizzle-kit studio", "db:drop": "drizzle-kit drop", "db:check": "drizzle-kit check:mysql", "db:up": "drizzle-kit up:mysql", "db:introspect": "drizzle-kit introspect:mysql", "db:migrate": "node scripts/migrate.js"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^16.3.1", "drizzle-orm": "^0.29.1", "express": "^4.18.2", "express-validator": "^7.0.1", "morgan": "^1.10.0", "mysql2": "^3.6.5", "uuidv4": "^6.2.13", "winston": "^3.11.0"}, "devDependencies": {"drizzle-kit": "^0.20.6", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-prettier": "^5.0.1", "prettier": "^3.1.0", "uuid": "^11.1.0"}}